const fs = require('fs-extra');
const path = require('path');
const xml2js = require('xml2js');
const ini = require('ini');
const chokidar = require('chokidar');
const { EventEmitter } = require('events');

class VortexManager extends EventEmitter {
  constructor() {
    super();
    this.vortexPath = '';
    this.gameDataPath = '';
    this.profilePath = '';
    this.watchers = new Map();
    this.modList = [];
    this.loadOrder = [];
  }

  /**
   * Initialize Vortex manager with paths
   */
  async initialize(vortexPath) {
    this.vortexPath = vortexPath;
    
    // Detect game and set paths
    await this.detectGamePaths();
    
    // Start file watchers
    this.startFileWatchers();
    
    // Load initial data
    await this.loadModData();
    
    this.emit('initialized');
  }

  /**
   * Detect game installation paths
   */
  async detectGamePaths() {
    const commonGamePaths = {
      'skyrimse': [
        'C:\\Program Files (x86)\\Steam\\steamapps\\common\\Skyrim Special Edition',
        'C:\\Program Files\\Steam\\steamapps\\common\\Skyrim Special Edition',
        'D:\\Steam\\steamapps\\common\\Skyrim Special Edition'
      ],
      'fallout4': [
        'C:\\Program Files (x86)\\Steam\\steamapps\\common\\Fallout 4',
        'C:\\Program Files\\Steam\\steamapps\\common\\Fallout 4',
        'D:\\Steam\\steamapps\\common\\Fallout 4'
      ]
    };

    // Try to find Vortex state file
    const statePath = path.join(this.vortexPath, 'state.json');
    if (await fs.pathExists(statePath)) {
      try {
        const state = await fs.readJson(statePath);
        const games = state.persistent?.gameMode?.known || {};
        
        // Find active game
        for (const [gameId, gameData] of Object.entries(games)) {
          if (gameData.discovered && gameData.discovered.path) {
            this.gameDataPath = path.join(gameData.discovered.path, 'Data');
            break;
          }
        }
      } catch (error) {
        console.error('Error reading Vortex state:', error);
      }
    }

    // Fallback to common paths if not found
    if (!this.gameDataPath) {
      for (const [game, paths] of Object.entries(commonGamePaths)) {
        for (const gamePath of paths) {
          const dataPath = path.join(gamePath, 'Data');
          if (await fs.pathExists(dataPath)) {
            this.gameDataPath = dataPath;
            break;
          }
        }
        if (this.gameDataPath) break;
      }
    }
  }

  /**
   * Start file watchers for mod files
   */
  startFileWatchers() {
    const watchPaths = [
      path.join(this.vortexPath, 'state.json'),
      path.join(this.gameDataPath, 'plugins.txt'),
      path.join(this.gameDataPath, 'loadorder.txt')
    ];

    watchPaths.forEach(watchPath => {
      if (fs.existsSync(watchPath)) {
        const watcher = chokidar.watch(watchPath, {
          persistent: true,
          ignoreInitial: true
        });

        watcher.on('change', () => {
          this.emit('fileChanged', watchPath);
          this.loadModData();
        });

        this.watchers.set(watchPath, watcher);
      }
    });
  }

  /**
   * Load mod data from Vortex files
   */
  async loadModData() {
    try {
      await Promise.all([
        this.loadModList(),
        this.loadLoadOrder()
      ]);
    } catch (error) {
      console.error('Error loading mod data:', error);
    }
  }

  /**
   * Load mod list from Vortex state
   */
  async loadModList() {
    const statePath = path.join(this.vortexPath, 'state.json');
    
    if (await fs.pathExists(statePath)) {
      try {
        const state = await fs.readJson(statePath);
        const mods = state.persistent?.mods || {};
        
        this.modList = Object.entries(mods).map(([id, mod]) => ({
          id,
          name: mod.attributes?.name || id,
          version: mod.attributes?.version || '1.0.0',
          enabled: mod.state === 'installed',
          category: mod.attributes?.category || 'Uncategorized',
          author: mod.attributes?.author || 'Unknown',
          description: mod.attributes?.shortDescription || '',
          installTime: mod.attributes?.installTime || Date.now(),
          fileSize: mod.attributes?.fileSize || 0,
          source: mod.attributes?.source || 'manual',
          dependencies: mod.rules?.filter(rule => rule.type === 'requires') || [],
          conflicts: mod.rules?.filter(rule => rule.type === 'conflicts') || []
        }));

        this.emit('modListUpdated', this.modList);
      } catch (error) {
        console.error('Error loading mod list:', error);
        this.modList = [];
      }
    }
  }

  /**
   * Load load order from plugins.txt
   */
  async loadLoadOrder() {
    const pluginsPath = path.join(this.gameDataPath, 'plugins.txt');
    
    if (await fs.pathExists(pluginsPath)) {
      try {
        const content = await fs.readFile(pluginsPath, 'utf8');
        this.loadOrder = content
          .split('\n')
          .map(line => line.trim())
          .filter(line => line && !line.startsWith('#'))
          .map((line, index) => {
            const enabled = line.startsWith('*');
            const plugin = enabled ? line.substring(1) : line;
            return {
              plugin,
              enabled,
              order: index,
              mod: this.findModByPlugin(plugin)
            };
          });

        this.emit('loadOrderUpdated', this.loadOrder);
      } catch (error) {
        console.error('Error loading load order:', error);
        this.loadOrder = [];
      }
    }
  }

  /**
   * Find mod by plugin name
   */
  findModByPlugin(pluginName) {
    return this.modList.find(mod => 
      mod.plugins && mod.plugins.includes(pluginName)
    );
  }

  /**
   * Save load order to plugins.txt
   */
  async saveLoadOrder(newLoadOrder) {
    const pluginsPath = path.join(this.gameDataPath, 'plugins.txt');
    
    try {
      const content = newLoadOrder
        .map(item => (item.enabled ? '*' : '') + item.plugin)
        .join('\n') + '\n';
      
      await fs.writeFile(pluginsPath, content, 'utf8');
      this.loadOrder = newLoadOrder;
      this.emit('loadOrderSaved', newLoadOrder);
      return true;
    } catch (error) {
      console.error('Error saving load order:', error);
      return false;
    }
  }

  /**
   * Get current mod list
   */
  getModList() {
    return this.modList;
  }

  /**
   * Get current load order
   */
  getLoadOrder() {
    return this.loadOrder;
  }

  /**
   * Clean up watchers
   */
  destroy() {
    this.watchers.forEach(watcher => watcher.close());
    this.watchers.clear();
  }
}

module.exports = { VortexManager };
