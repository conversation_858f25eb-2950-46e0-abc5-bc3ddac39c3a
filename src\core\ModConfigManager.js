const fs = require('fs-extra');
const path = require('path');
const { EventEmitter } = require('events');
const _ = require('lodash');

class ModConfigManager extends EventEmitter {
  constructor() {
    super();
    this.conflicts = [];
    this.dependencies = new Map();
    this.resolutionStrategies = new Map();
  }

  /**
   * Detect conflicts between mods
   */
  async detectConflicts(modList, loadOrder) {
    this.conflicts = [];
    
    // Check for circular dependencies
    const circularDeps = this.detectCircularDependencies(modList);
    this.conflicts.push(...circularDeps);
    
    // Check for missing dependencies
    const missingDeps = this.detectMissingDependencies(modList);
    this.conflicts.push(...missingDeps);
    
    // Check for file conflicts
    const fileConflicts = await this.detectFileConflicts(modList);
    this.conflicts.push(...fileConflicts);
    
    // Check for load order conflicts
    const loadOrderConflicts = this.detectLoadOrderConflicts(modList, loadOrder);
    this.conflicts.push(...loadOrderConflicts);
    
    this.emit('conflictsDetected', this.conflicts);
    return this.conflicts;
  }

  /**
   * Detect circular dependencies
   */
  detectCircularDependencies(modList) {
    const conflicts = [];
    const visited = new Set();
    const recursionStack = new Set();
    
    const dfs = (modId, path = []) => {
      if (recursionStack.has(modId)) {
        // Found circular dependency
        const cycleStart = path.indexOf(modId);
        const cycle = path.slice(cycleStart).concat([modId]);
        
        conflicts.push({
          id: `circular_${Date.now()}_${Math.random()}`,
          type: 'circular_dependency',
          severity: 'high',
          title: 'Circular Dependency Detected',
          description: `Circular dependency found: ${cycle.join(' → ')}`,
          affectedMods: cycle,
          cycle: cycle,
          resolutions: [
            {
              type: 'remove_dependency',
              description: 'Remove one of the dependency rules',
              action: 'remove_rule'
            },
            {
              type: 'reorder_mods',
              description: 'Manually reorder mods to break the cycle',
              action: 'manual_reorder'
            }
          ]
        });
        return;
      }
      
      if (visited.has(modId)) {
        return;
      }
      
      visited.add(modId);
      recursionStack.add(modId);
      
      const mod = modList.find(m => m.id === modId);
      if (mod && mod.dependencies) {
        for (const dep of mod.dependencies) {
          dfs(dep.reference, [...path, modId]);
        }
      }
      
      recursionStack.delete(modId);
    };
    
    modList.forEach(mod => {
      if (!visited.has(mod.id)) {
        dfs(mod.id);
      }
    });
    
    return conflicts;
  }

  /**
   * Detect missing dependencies
   */
  detectMissingDependencies(modList) {
    const conflicts = [];
    const modIds = new Set(modList.map(mod => mod.id));
    
    modList.forEach(mod => {
      if (mod.dependencies) {
        mod.dependencies.forEach(dep => {
          if (!modIds.has(dep.reference)) {
            conflicts.push({
              id: `missing_${mod.id}_${dep.reference}`,
              type: 'missing_dependency',
              severity: 'high',
              title: 'Missing Dependency',
              description: `Mod "${mod.name}" requires "${dep.reference}" which is not installed`,
              affectedMods: [mod.id],
              missingDependency: dep.reference,
              resolutions: [
                {
                  type: 'install_dependency',
                  description: `Install the required mod: ${dep.reference}`,
                  action: 'install_mod'
                },
                {
                  type: 'remove_dependency_rule',
                  description: 'Remove the dependency requirement',
                  action: 'remove_rule'
                },
                {
                  type: 'disable_mod',
                  description: `Disable "${mod.name}"`,
                  action: 'disable_mod'
                }
              ]
            });
          }
        });
      }
    });
    
    return conflicts;
  }

  /**
   * Detect file conflicts between mods
   */
  async detectFileConflicts(modList) {
    const conflicts = [];
    const fileMap = new Map(); // file -> [mods that modify it]
    
    // This would need to scan actual mod files
    // For now, we'll simulate based on common conflict patterns
    const commonConflicts = [
      {
        file: 'meshes/landscape/riften/riftenground01.nif',
        mods: ['Skyrim 202X', 'Terrain Parallax', 'SMIM']
      },
      {
        file: 'textures/landscape/riften/riftenground01.dds',
        mods: ['Skyrim 202X', 'Seasonal Landscapes']
      }
    ];
    
    commonConflicts.forEach(conflict => {
      const affectedMods = modList.filter(mod => 
        conflict.mods.some(conflictMod => 
          mod.name.toLowerCase().includes(conflictMod.toLowerCase())
        )
      );
      
      if (affectedMods.length > 1) {
        conflicts.push({
          id: `file_conflict_${conflict.file.replace(/[^a-zA-Z0-9]/g, '_')}`,
          type: 'file_conflict',
          severity: 'medium',
          title: 'File Conflict',
          description: `Multiple mods modify the same file: ${conflict.file}`,
          affectedMods: affectedMods.map(mod => mod.id),
          conflictingFile: conflict.file,
          resolutions: [
            {
              type: 'load_order_priority',
              description: 'Adjust load order to prioritize one mod',
              action: 'reorder_mods'
            },
            {
              type: 'create_patch',
              description: 'Create a compatibility patch',
              action: 'create_patch'
            },
            {
              type: 'disable_conflicting_mod',
              description: 'Disable one of the conflicting mods',
              action: 'disable_mod'
            }
          ]
        });
      }
    });
    
    return conflicts;
  }

  /**
   * Detect load order conflicts
   */
  detectLoadOrderConflicts(modList, loadOrder) {
    const conflicts = [];
    
    // Check if dependencies are loaded before dependents
    modList.forEach(mod => {
      if (mod.dependencies) {
        mod.dependencies.forEach(dep => {
          const modIndex = loadOrder.findIndex(item => item.mod?.id === mod.id);
          const depIndex = loadOrder.findIndex(item => item.mod?.id === dep.reference);
          
          if (modIndex !== -1 && depIndex !== -1 && modIndex < depIndex) {
            conflicts.push({
              id: `load_order_${mod.id}_${dep.reference}`,
              type: 'load_order_conflict',
              severity: 'medium',
              title: 'Load Order Conflict',
              description: `"${mod.name}" loads before its dependency "${dep.reference}"`,
              affectedMods: [mod.id, dep.reference],
              resolutions: [
                {
                  type: 'auto_reorder',
                  description: 'Automatically fix load order',
                  action: 'auto_reorder'
                },
                {
                  type: 'manual_reorder',
                  description: 'Manually adjust load order',
                  action: 'manual_reorder'
                }
              ]
            });
          }
        });
      }
    });
    
    return conflicts;
  }

  /**
   * Resolve a specific conflict
   */
  async resolveConflict(conflictId, resolution) {
    const conflict = this.conflicts.find(c => c.id === conflictId);
    if (!conflict) {
      throw new Error(`Conflict ${conflictId} not found`);
    }
    
    switch (resolution.action) {
      case 'remove_rule':
        return this.removeConflictingRule(conflict);
      case 'disable_mod':
        return this.disableMod(conflict, resolution.modId);
      case 'reorder_mods':
        return this.reorderMods(conflict, resolution.newOrder);
      case 'auto_reorder':
        return this.autoReorderMods(conflict);
      default:
        throw new Error(`Unknown resolution action: ${resolution.action}`);
    }
  }

  /**
   * Auto-reorder mods to resolve conflicts
   */
  async autoReorderMods(conflict) {
    // Implement topological sort for dependency resolution
    // This is a simplified version
    this.emit('conflictResolved', conflict.id);
    return true;
  }

  /**
   * Get suggested load order based on dependencies
   */
  getSuggestedLoadOrder(modList) {
    // Implement topological sort
    const graph = new Map();
    const inDegree = new Map();
    
    // Build dependency graph
    modList.forEach(mod => {
      graph.set(mod.id, []);
      inDegree.set(mod.id, 0);
    });
    
    modList.forEach(mod => {
      if (mod.dependencies) {
        mod.dependencies.forEach(dep => {
          if (graph.has(dep.reference)) {
            graph.get(dep.reference).push(mod.id);
            inDegree.set(mod.id, inDegree.get(mod.id) + 1);
          }
        });
      }
    });
    
    // Topological sort
    const queue = [];
    const result = [];
    
    inDegree.forEach((degree, modId) => {
      if (degree === 0) {
        queue.push(modId);
      }
    });
    
    while (queue.length > 0) {
      const current = queue.shift();
      result.push(current);
      
      graph.get(current).forEach(neighbor => {
        inDegree.set(neighbor, inDegree.get(neighbor) - 1);
        if (inDegree.get(neighbor) === 0) {
          queue.push(neighbor);
        }
      });
    }
    
    return result;
  }

  /**
   * Get current conflicts
   */
  getConflicts() {
    return this.conflicts;
  }
}

module.exports = { ModConfigManager };
