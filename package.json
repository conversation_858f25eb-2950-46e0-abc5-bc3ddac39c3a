{"name": "vortex-mcp", "version": "1.0.0", "description": "Mod Configuration Panel for Vortex Mod Manager", "main": "src/index.js", "scripts": {"start": "electron .", "dev": "electron . --dev", "build": "electron-builder", "test": "jest", "lint": "eslint src/"}, "keywords": ["vortex", "mod", "configuration", "skyrim", "fallout", "gaming"], "author": "Vortex MCP Team", "license": "MIT", "devDependencies": {"electron": "^27.0.0", "electron-builder": "^24.6.4", "eslint": "^8.50.0", "jest": "^29.7.0"}, "dependencies": {"react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.15.0", "electron-store": "^8.1.0", "fs-extra": "^11.1.1", "xml2js": "^0.6.2", "ini": "^4.1.1", "chokidar": "^3.5.3", "lodash": "^4.17.21", "uuid": "^9.0.0"}, "build": {"appId": "com.vortexmcp.app", "productName": "Vortex MCP", "directories": {"output": "dist"}, "files": ["src/**/*", "node_modules/**/*"], "win": {"target": "nsis", "icon": "assets/icon.ico"}, "linux": {"target": "AppImage", "icon": "assets/icon.png"}, "mac": {"target": "dmg", "icon": "assets/icon.icns"}}}