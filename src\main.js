const { app, BrowserWindow, ipc<PERSON>ain, dialog, Menu } = require('electron');
const path = require('path');
const fs = require('fs-extra');
const Store = require('electron-store');
const { VortexManager } = require('./core/VortexManager');
const { ModConfigManager } = require('./core/ModConfigManager');

// Initialize electron store for persistent settings
const store = new Store();

let mainWindow;
let vortexManager;
let modConfigManager;

function createWindow() {
  // Create the browser window
  mainWindow = new BrowserWindow({
    width: 1400,
    height: 900,
    minWidth: 1200,
    minHeight: 700,
    webPreferences: {
      nodeIntegration: true,
      contextIsolation: false,
      enableRemoteModule: true
    },
    icon: path.join(__dirname, '../assets/icon.png'),
    show: false
  });

  // Load the app
  if (process.env.NODE_ENV === 'development') {
    mainWindow.loadFile(path.join(__dirname, 'renderer/index.html'));
    mainWindow.webContents.openDevTools();
  } else {
    mainWindow.loadFile(path.join(__dirname, 'renderer/index.html'));
  }

  // Show window when ready
  mainWindow.once('ready-to-show', () => {
    mainWindow.show();
  });

  // Handle window closed
  mainWindow.on('closed', () => {
    mainWindow = null;
  });

  // Initialize managers
  vortexManager = new VortexManager();
  modConfigManager = new ModConfigManager();
}

// Create application menu
function createMenu() {
  const template = [
    {
      label: 'File',
      submenu: [
        {
          label: 'Open Vortex Directory',
          click: async () => {
            const result = await dialog.showOpenDialog(mainWindow, {
              properties: ['openDirectory'],
              title: 'Select Vortex Installation Directory'
            });
            
            if (!result.canceled) {
              const vortexPath = result.filePaths[0];
              store.set('vortexPath', vortexPath);
              mainWindow.webContents.send('vortex-path-selected', vortexPath);
            }
          }
        },
        {
          label: 'Import Mod List',
          click: async () => {
            const result = await dialog.showOpenDialog(mainWindow, {
              properties: ['openFile'],
              filters: [
                { name: 'Text Files', extensions: ['txt'] },
                { name: 'All Files', extensions: ['*'] }
              ],
              title: 'Import Mod List'
            });
            
            if (!result.canceled) {
              const modListPath = result.filePaths[0];
              mainWindow.webContents.send('import-mod-list', modListPath);
            }
          }
        },
        { type: 'separator' },
        {
          label: 'Exit',
          click: () => {
            app.quit();
          }
        }
      ]
    },
    {
      label: 'Tools',
      submenu: [
        {
          label: 'Scan for Conflicts',
          click: () => {
            mainWindow.webContents.send('scan-conflicts');
          }
        },
        {
          label: 'Optimize Load Order',
          click: () => {
            mainWindow.webContents.send('optimize-load-order');
          }
        },
        {
          label: 'Create Backup',
          click: () => {
            mainWindow.webContents.send('create-backup');
          }
        }
      ]
    },
    {
      label: 'Help',
      submenu: [
        {
          label: 'About',
          click: () => {
            dialog.showMessageBox(mainWindow, {
              type: 'info',
              title: 'About Vortex MCP',
              message: 'Vortex Mod Configuration Panel',
              detail: 'Version 1.0.0\nA comprehensive tool for managing Vortex mod configurations.'
            });
          }
        }
      ]
    }
  ];

  const menu = Menu.buildFromTemplate(template);
  Menu.setApplicationMenu(menu);
}

// App event handlers
app.whenReady().then(() => {
  createWindow();
  createMenu();

  app.on('activate', () => {
    if (BrowserWindow.getAllWindows().length === 0) {
      createWindow();
    }
  });
});

app.on('window-all-closed', () => {
  if (process.platform !== 'darwin') {
    app.quit();
  }
});

// IPC handlers
ipcMain.handle('get-vortex-path', () => {
  return store.get('vortexPath', '');
});

ipcMain.handle('set-vortex-path', (event, path) => {
  store.set('vortexPath', path);
  return true;
});

ipcMain.handle('get-mod-list', async () => {
  try {
    return await vortexManager.getModList();
  } catch (error) {
    console.error('Error getting mod list:', error);
    return [];
  }
});

ipcMain.handle('get-load-order', async () => {
  try {
    return await vortexManager.getLoadOrder();
  } catch (error) {
    console.error('Error getting load order:', error);
    return [];
  }
});

ipcMain.handle('save-load-order', async (event, loadOrder) => {
  try {
    return await vortexManager.saveLoadOrder(loadOrder);
  } catch (error) {
    console.error('Error saving load order:', error);
    return false;
  }
});

ipcMain.handle('detect-conflicts', async () => {
  try {
    return await modConfigManager.detectConflicts();
  } catch (error) {
    console.error('Error detecting conflicts:', error);
    return [];
  }
});

ipcMain.handle('resolve-conflict', async (event, conflictId, resolution) => {
  try {
    return await modConfigManager.resolveConflict(conflictId, resolution);
  } catch (error) {
    console.error('Error resolving conflict:', error);
    return false;
  }
});
